import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import '../services/data_sync_service.dart';

/// Data synchronization state
class DataSyncState {
  final bool isLoading;
  final bool isFresh;
  final DateTime? lastUpdate;
  final String? error;
  final Map<String, dynamic>? data;

  const DataSyncState({
    this.isLoading = false,
    this.isFresh = false,
    this.lastUpdate,
    this.error,
    this.data,
  });

  DataSyncState copyWith({
    bool? isLoading,
    bool? isFresh,
    DateTime? lastUpdate,
    String? error,
    Map<String, dynamic>? data,
  }) {
    return DataSyncState(
      isLoading: isLoading ?? this.isLoading,
      isFresh: isFresh ?? this.isFresh,
      lastUpdate: lastUpdate ?? this.lastUpdate,
      error: error ?? this.error,
      data: data ?? this.data,
    );
  }
}

/// Base data sync notifier
abstract class DataSyncNotifier extends StateNotifier<DataSyncState> {
  final DataSyncService _dataSyncService;
  final String dataType;

  DataSyncNotifier(this._dataSyncService, this.dataType) : super(const DataSyncState()) {
    _initializeData();
  }

  /// Initialize data from cache and check freshness
  Future<void> _initializeData() async {
    try {
      debugPrint('📊 Initializing data for $dataType');
      
      // Load cached data first
      final cachedData = await _dataSyncService.getCachedData(dataType);
      if (cachedData != null) {
        state = state.copyWith(data: cachedData);
        debugPrint('📊 Loaded cached data for $dataType');
      }

      // Check if data is fresh
      final isFresh = await _dataSyncService.isDataFresh(dataType);
      state = state.copyWith(isFresh: isFresh);

      // If data is stale, trigger refresh
      if (!isFresh) {
        debugPrint('📊 Data is stale for $dataType, triggering refresh');
        await refreshData();
      }
    } catch (e) {
      debugPrint('📊 Error initializing data for $dataType: $e');
      state = state.copyWith(error: e.toString());
    }
  }

  /// Refresh data from API
  Future<void> refreshData({bool force = false}) async {
    if (_dataSyncService.isRefreshActive(dataType) && !force) {
      debugPrint('📊 Refresh already active for $dataType, skipping');
      return;
    }

    try {
      _dataSyncService.markRefreshActive(dataType);
      state = state.copyWith(isLoading: true, error: null);

      debugPrint('📊 Refreshing data for $dataType');
      
      // Fetch fresh data from API
      final freshData = await fetchDataFromAPI();
      
      if (freshData != null) {
        // Check if data has actually changed
        final hasChanged = await _dataSyncService.hasDataChanged(dataType, freshData);
        
        if (hasChanged || force) {
          // Save to cache
          await _dataSyncService.saveDataToCache(dataType, freshData);
          
          // Update state
          state = state.copyWith(
            data: freshData,
            isFresh: true,
            lastUpdate: DateTime.now(),
            isLoading: false,
          );
          
          debugPrint('📊 Data refreshed and cached for $dataType');
        } else {
          // Data hasn't changed, just update freshness
          state = state.copyWith(
            isFresh: true,
            lastUpdate: DateTime.now(),
            isLoading: false,
          );
          
          debugPrint('📊 Data unchanged for $dataType, updated freshness only');
        }
      } else {
        state = state.copyWith(
          isLoading: false,
          error: 'Failed to fetch data from API',
        );
      }
    } catch (e) {
      debugPrint('📊 Error refreshing data for $dataType: $e');
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    } finally {
      _dataSyncService.markRefreshComplete(dataType);
    }
  }

  /// Request debounced refresh
  void requestDebouncedRefresh() {
    _dataSyncService.requestRefresh(dataType, () => refreshData());
  }

  /// Invalidate cache and force refresh
  Future<void> invalidateAndRefresh() async {
    await _dataSyncService.invalidateCache(dataType);
    await refreshData(force: true);
  }

  /// Abstract method to be implemented by specific data types
  Future<Map<String, dynamic>?> fetchDataFromAPI();

  @override
  void dispose() {
    super.dispose();
  }
}

/// Wallet data sync notifier
class WalletDataSyncNotifier extends DataSyncNotifier {
  WalletDataSyncNotifier(DataSyncService dataSyncService) 
      : super(dataSyncService, DataTypes.wallet);

  @override
  Future<Map<String, dynamic>?> fetchDataFromAPI() async {
    try {
      // PRODUCTION: This should call actual wallet API - NO MOCK DATA
      debugPrint('❌ WALLET_SYNC: Mock data provider should not be used in production');
      throw Exception('Mock wallet data provider used in production app');

      // TODO: Implement actual wallet API call
      // return await ApiService().get('/wallet/data');
    } catch (e) {
      debugPrint('❌ WALLET_SYNC: Error in wallet data fetch: $e');
      return null;
    }
  }
}

/// Profile data sync notifier
class ProfileDataSyncNotifier extends DataSyncNotifier {
  ProfileDataSyncNotifier(DataSyncService dataSyncService) 
      : super(dataSyncService, DataTypes.profile);

  @override
  Future<Map<String, dynamic>?> fetchDataFromAPI() async {
    try {
      // TODO: Replace with actual profile API call
      debugPrint('📊 Fetching profile data from API');
      
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      // Mock profile data
      return {
        'id': 'user_123',
        'name': 'John Doe',
        'email': '<EMAIL>',
        'phone': '+91 **********',
        'profilePicture': null,
        'preferences': {
          'notifications': true,
          'darkMode': false,
          'language': 'en',
        },
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('📊 Error fetching profile data: $e');
      return null;
    }
  }
}

/// Dashboard data sync notifier
class DashboardDataSyncNotifier extends DataSyncNotifier {
  DashboardDataSyncNotifier(DataSyncService dataSyncService) 
      : super(dataSyncService, DataTypes.dashboard);

  @override
  Future<Map<String, dynamic>?> fetchDataFromAPI() async {
    try {
      // TODO: Replace with actual dashboard API call
      debugPrint('📊 Fetching dashboard data from API');
      
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));
      
      // Mock dashboard data
      return {
        'nearestStations': [
          {
            'id': 'station_001',
            'name': 'Central Mall Charging Hub',
            'distance': 2.5,
            'availability': 'available',
            'connectorTypes': ['Type 2', 'CCS'],
          },
          {
            'id': 'station_002',
            'name': 'Tech Park Station',
            'distance': 3.8,
            'availability': 'busy',
            'connectorTypes': ['Type 2'],
          },
        ],
        'userLocation': {
          'latitude': 28.6139,
          'longitude': 77.2090,
        },
        'lastUpdated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('📊 Error fetching dashboard data: $e');
      return null;
    }
  }
}

/// Data sync service provider
final dataSyncServiceProvider = Provider<DataSyncService>((ref) {
  return DataSyncService();
});

/// Wallet data sync provider
final walletDataSyncProvider = StateNotifierProvider<WalletDataSyncNotifier, DataSyncState>((ref) {
  final dataSyncService = ref.watch(dataSyncServiceProvider);
  return WalletDataSyncNotifier(dataSyncService);
});

/// Profile data sync provider
final profileDataSyncProvider = StateNotifierProvider<ProfileDataSyncNotifier, DataSyncState>((ref) {
  final dataSyncService = ref.watch(dataSyncServiceProvider);
  return ProfileDataSyncNotifier(dataSyncService);
});

/// Dashboard data sync provider
final dashboardDataSyncProvider = StateNotifierProvider<DashboardDataSyncNotifier, DataSyncState>((ref) {
  final dataSyncService = ref.watch(dataSyncServiceProvider);
  return DashboardDataSyncNotifier(dataSyncService);
});

/// Combined data freshness provider
final dataFreshnessProvider = Provider<Map<String, bool>>((ref) {
  final walletState = ref.watch(walletDataSyncProvider);
  final profileState = ref.watch(profileDataSyncProvider);
  final dashboardState = ref.watch(dashboardDataSyncProvider);

  return {
    DataTypes.wallet: walletState.isFresh,
    DataTypes.profile: profileState.isFresh,
    DataTypes.dashboard: dashboardState.isFresh,
  };
});

/// Loading states provider
final dataLoadingProvider = Provider<Map<String, bool>>((ref) {
  final walletState = ref.watch(walletDataSyncProvider);
  final profileState = ref.watch(profileDataSyncProvider);
  final dashboardState = ref.watch(dashboardDataSyncProvider);

  return {
    DataTypes.wallet: walletState.isLoading,
    DataTypes.profile: profileState.isLoading,
    DataTypes.dashboard: dashboardState.isLoading,
  };
});
