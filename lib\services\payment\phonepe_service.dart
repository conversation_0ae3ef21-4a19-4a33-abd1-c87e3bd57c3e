import 'package:phonepe_payment_sdk/phonepe_payment_sdk.dart';
import 'package:flutter/foundation.dart';
import 'dart:async';
import 'dart:convert';
import '../../shared/utils/error_handler.dart';

/// Enum for different payment result types based on PhonePe official documentation
enum PaymentResultType {
  success,
  cancelled,
  failed,
  pending,
  timeout,
  networkError,
  appCrash,
  invalidResponse,
  backPressed,
  interrupted,
  unknown
}

/// Payment result model with comprehensive error handling
class PaymentResult {
  final PaymentResultType type;
  final String message;
  final Map<String, dynamic>? data;
  final String? errorCode;
  final bool isUserCancelled;

  const PaymentResult({
    required this.type,
    required this.message,
    this.data,
    this.errorCode,
    this.isUserCancelled = false,
  });

  factory PaymentResult.success(Map<String, dynamic> data) {
    return PaymentResult(
      type: PaymentResultType.success,
      message: 'Payment completed successfully',
      data: data,
    );
  }

  factory PaymentResult.cancelled() {
    return const PaymentResult(
      type: PaymentResultType.cancelled,
      message: 'Payment cancelled by user',
      isUserCancelled: true,
    );
  }

  factory PaymentResult.pending(String message, {Map<String, dynamic>? data}) {
    return PaymentResult(
      type: PaymentResultType.pending,
      message: message,
      data: data,
    );
  }

  factory PaymentResult.interrupted(String message, {Map<String, dynamic>? data}) {
    return PaymentResult(
      type: PaymentResultType.interrupted,
      message: message,
      data: data,
    );
  }

  factory PaymentResult.failed(String message,
      {String? errorCode, Map<String, dynamic>? data}) {
    return PaymentResult(
      type: PaymentResultType.failed,
      message: message,
      errorCode: errorCode,
      data: data,
    );
  }

  factory PaymentResult.timeout() {
    return const PaymentResult(
      type: PaymentResultType.timeout,
      message: 'Payment request timed out. Please try again.',
    );
  }

  factory PaymentResult.networkError() {
    return const PaymentResult(
      type: PaymentResultType.networkError,
      message:
          'Network error occurred. Please check your connection and try again.',
    );
  }

  factory PaymentResult.appCrash() {
    return const PaymentResult(
      type: PaymentResultType.appCrash,
      message: 'PhonePe app encountered an error. Please try again.',
    );
  }

  factory PaymentResult.invalidResponse(String details) {
    return PaymentResult(
      type: PaymentResultType.invalidResponse,
      message: 'Invalid response from payment gateway',
      data: {'details': details},
    );
  }

  factory PaymentResult.backPressed() {
    return const PaymentResult(
      type: PaymentResultType.backPressed,
      message: 'Payment cancelled - back button pressed',
      isUserCancelled: true,
    );
  }

  factory PaymentResult.unknown(String details) {
    return PaymentResult(
      type: PaymentResultType.unknown,
      message: 'Unknown error occurred during payment',
      data: {'details': details},
    );
  }
}

/// Service wrapper for PhonePe payment SDK integration with comprehensive exception handling
class PhonePeService {
  static bool _isInitialized = false;
  static Timer? _paymentTimeoutTimer;
  static Completer<PaymentResult>? _paymentCompleter;

  // CRITICAL FIX: Add server notification callback mechanism (similar to PayU)
  static Function(PaymentResult, String)? _serverNotificationCallback;

  /// Initialize PhonePe SDK (v3.0.0 API)
  static Future<bool> init({
    required String environment,
    required String merchantId,
    required String flowId,
    bool enableLogging = false,
  }) async {
    debugPrint('🔔 PHONEPE: ========== SDK INITIALIZATION START ==========');
    debugPrint('🔔 PHONEPE: Checking initialization status...');
    debugPrint('🔔 PHONEPE: Current _isInitialized: $_isInitialized');

    if (_isInitialized) {
      debugPrint('🔔 PHONEPE: SDK already initialized, returning true');
      debugPrint(
          '🔔 PHONEPE: ========== SDK INITIALIZATION END (CACHED) ==========');
      return true;
    }

    debugPrint('🔔 PHONEPE: Starting fresh SDK initialization...');
    debugPrint('🔔 PHONEPE: Input Parameters:');
    debugPrint('🔔 PHONEPE:   - Environment: "$environment"');
    debugPrint('🔔 PHONEPE:   - MerchantId: "$merchantId"');
    debugPrint('🔔 PHONEPE:   - FlowId: "$flowId"');
    debugPrint('🔔 PHONEPE:   - EnableLogging: $enableLogging');
    debugPrint('🔔 PHONEPE:   - SDK Version: v3.0.0');

    // CRITICAL FIX: Comprehensive parameter validation according to PhonePe documentation
    debugPrint('🔔 PHONEPE: Validating initialization parameters...');

    if (environment.isEmpty) {
      debugPrint('❌ PHONEPE: Environment parameter is empty');
      debugPrint('🔔 PHONEPE: ========== SDK INITIALIZATION FAILED (INVALID PARAMS) ==========');
      return false;
    }

    if (merchantId.isEmpty) {
      debugPrint('❌ PHONEPE: Merchant ID parameter is empty');
      debugPrint('🔔 PHONEPE: ========== SDK INITIALIZATION FAILED (INVALID PARAMS) ==========');
      return false;
    }

    if (flowId.isEmpty) {
      debugPrint('❌ PHONEPE: Flow ID parameter is empty');
      debugPrint('🔔 PHONEPE: ========== SDK INITIALIZATION FAILED (INVALID PARAMS) ==========');
      return false;
    }

    // Validate environment values according to PhonePe documentation
    final validEnvironments = ['SANDBOX', 'PRODUCTION'];
    if (!validEnvironments.contains(environment.toUpperCase())) {
      debugPrint('❌ PHONEPE: Invalid environment "$environment". Must be SANDBOX or PRODUCTION');
      debugPrint('🔔 PHONEPE: ========== SDK INITIALIZATION FAILED (INVALID ENVIRONMENT) ==========');
      return false;
    }

    // Reset any existing state to prevent conflicts
    debugPrint('🔔 PHONEPE: Resetting SDK state...');
    _paymentCompleter = null;
    _serverNotificationCallback = null;

    debugPrint('✅ PHONEPE: Parameter validation successful');

    try {
      debugPrint('🔔 PHONEPE: Calling PhonePePaymentSdk.init()...');
      final startTime = DateTime.now();

      // SDK v3.0.0 expects: environment, merchantId, flowId, enableLogging
      // CRITICAL FIX: Add timeout handling for SDK initialization
      final inited = await PhonePePaymentSdk.init(
          environment, merchantId, flowId, enableLogging)
          .timeout(
            Duration(seconds: 30), // 30 second timeout for initialization
            onTimeout: () {
              debugPrint('⏰ PHONEPE: SDK initialization timed out after 30 seconds');
              return false;
            },
          );

      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      debugPrint(
          '🔔 PHONEPE: SDK initialization completed in ${duration.inMilliseconds}ms');
      debugPrint('🔔 PHONEPE: Initialization result: $inited');

      _isInitialized = inited;

      if (inited) {
        debugPrint('✅ PHONEPE: SDK initialization SUCCESSFUL');
        debugPrint('🔔 PHONEPE: SDK State Updated:');
        debugPrint('🔔 PHONEPE:   - _isInitialized: $_isInitialized');
        debugPrint('🔔 PHONEPE:   - Environment: $environment');
        debugPrint('🔔 PHONEPE:   - MerchantId: $merchantId');
        debugPrint('🔔 PHONEPE:   - FlowId: $flowId');
        debugPrint('🔔 PHONEPE:   - Logging Enabled: $enableLogging');
      } else {
        debugPrint('❌ PHONEPE: SDK initialization FAILED');
        debugPrint('🔔 PHONEPE: SDK returned false - check configuration');
      }

      debugPrint('🔔 PHONEPE: ========== SDK INITIALIZATION END ==========');
      return inited;
    } catch (e, stackTrace) {
      debugPrint('❌ PHONEPE: SDK initialization EXCEPTION');
      debugPrint('🔔 PHONEPE: Exception Type: ${e.runtimeType}');
      debugPrint('🔔 PHONEPE: Exception Message: $e');
      debugPrint('🔔 PHONEPE: Stack Trace: $stackTrace');
      debugPrint(
          '🔔 PHONEPE: ========== SDK INITIALIZATION END (ERROR) ==========');
      return false;
    }
  }

  /// Start a PG transaction with comprehensive exception handling (v3.0.0 API)
  static Future<PaymentResult> startPGTransaction({
    required String request,
    required String appSchema,
    Duration timeout = const Duration(minutes: 5),
  }) async {
    debugPrint('💳 PAYMENT: ========== TRANSACTION START ==========');
    debugPrint('💳 PAYMENT: Initiating PhonePe transaction...');
    debugPrint('💳 PAYMENT: SDK Version: v3.0.0');
    debugPrint('💳 PAYMENT: Timeout Duration: ${timeout.inMinutes} minutes');

    // CRITICAL FIX: Comprehensive input parameter validation according to PhonePe docs
    debugPrint('💳 PAYMENT: Validating input parameters...');

    if (request.isEmpty) {
      debugPrint('❌ PAYMENT: Request parameter is empty');
      return PaymentResult.failed('Invalid payment request. Please try again.');
    }

    if (appSchema.isEmpty) {
      debugPrint('❌ PAYMENT: App schema parameter is empty');
      return PaymentResult.failed('Payment configuration error. Please contact support.');
    }

    // Validate request is base64 encoded (PhonePe requirement)
    try {
      // Try to decode base64 to validate format
      final decoded = base64.decode(request);
      debugPrint('💳 PAYMENT: Request validation successful (${decoded.length} bytes decoded)');
    } catch (e) {
      debugPrint('❌ PAYMENT: Invalid request format - not valid base64: $e');
      return PaymentResult.failed('Invalid payment request format. Please try again.');
    }

    debugPrint('💳 PAYMENT: Request length: ${request.length} characters');
    debugPrint('💳 PAYMENT: App Schema: "$appSchema"');
    debugPrint(
        '💳 PAYMENT: Request preview: ${request.length > 100 ? "${request.substring(0, 100)}..." : request}');

    debugPrint('✅ PAYMENT: Parameter validation successful');

    // Check SDK initialization status
    debugPrint('💳 PAYMENT: Checking SDK initialization status...');
    debugPrint('💳 PAYMENT: SDK Initialized: $_isInitialized');

    if (!_isInitialized) {
      debugPrint(
          '❌ PAYMENT: SDK not initialized! Cannot proceed with transaction');
      return PaymentResult.failed(
          'Payment service is temporarily unavailable. Please try again later.');
    }

    // Cancel any existing payment flow
    debugPrint('💳 PAYMENT: Cleaning up any existing payment flows...');
    _cancelExistingPayment();

    // Create a new completer for this payment
    debugPrint('💳 PAYMENT: Creating new payment completer...');
    _paymentCompleter = Completer<PaymentResult>();

    try {
      debugPrint('💳 PAYMENT: Setting up payment timeout timer...');
      final timeoutStartTime = DateTime.now();

      // Set up timeout timer
      _paymentTimeoutTimer = Timer(timeout, () {
        final timeoutEndTime = DateTime.now();
        final actualTimeout = timeoutEndTime.difference(timeoutStartTime);

        debugPrint('⏰ PAYMENT: TIMEOUT TRIGGERED');
        debugPrint(
            '⏰ PAYMENT: Timeout after ${actualTimeout.inMinutes}m ${actualTimeout.inSeconds % 60}s');
        debugPrint('⏰ PAYMENT: Expected timeout: ${timeout.inMinutes} minutes');

        if (!_paymentCompleter!.isCompleted) {
          debugPrint('⏰ PAYMENT: Completing payment with timeout result');
          _paymentCompleter!.complete(PaymentResult.timeout());
        } else {
          debugPrint('⏰ PAYMENT: Payment already completed, ignoring timeout');
        }
      });

      debugPrint('💳 PAYMENT: Calling PhonePe SDK startTransaction()...');
      debugPrint(
          '💳 PAYMENT: SDK Method: PhonePePaymentSdk.startTransaction()');
      debugPrint('💳 PAYMENT: Parameter 1 (request): ${request.length} chars');
      debugPrint('💳 PAYMENT: Parameter 2 (appSchema): "$appSchema"');

      final sdkCallStartTime = DateTime.now();

      // Call the PhonePe SDK v3.0.0 - only accepts 2 parameters: request and appSchema
      final result = await PhonePePaymentSdk.startTransaction(
        request,
        appSchema,
      ).timeout(
        timeout,
        onTimeout: () {
          final sdkTimeoutTime = DateTime.now();
          final sdkDuration = sdkTimeoutTime.difference(sdkCallStartTime);

          debugPrint('⏰ PAYMENT: PhonePe SDK call TIMED OUT');
          debugPrint(
              '⏰ PAYMENT: SDK call duration: ${sdkDuration.inSeconds} seconds');
          debugPrint('⏰ PAYMENT: Returning null from timeout handler');
          return null;
        },
      );

      final sdkCallEndTime = DateTime.now();
      final sdkCallDuration = sdkCallEndTime.difference(sdkCallStartTime);

      // Cancel timeout timer since we got a response
      debugPrint(
          '💳 PAYMENT: SDK call completed in ${sdkCallDuration.inSeconds} seconds');
      debugPrint('💳 PAYMENT: Cancelling timeout timer...');
      _paymentTimeoutTimer?.cancel();

      debugPrint('📱 RESPONSE: ========== SDK RESPONSE RECEIVED ==========');
      debugPrint('📱 RESPONSE: Raw result type: ${result.runtimeType}');
      debugPrint('📱 RESPONSE: Raw result: $result');

      if (result == null) {
        debugPrint(
            '📱 RESPONSE: Result is NULL - possible timeout or cancellation');
      } else {
        debugPrint('📱 RESPONSE: Result is not null, processing...');
        if (result is Map) {
          debugPrint('📱 RESPONSE: Result is Map with ${result.length} keys');
          for (final entry in result.entries) {
            debugPrint('📱 RESPONSE:   - ${entry.key}: ${entry.value}');
          }
        }
      }

      // Process the result
      debugPrint('💳 PAYMENT: Processing SDK result...');
      final paymentResult =
          _processPaymentResult(result?.cast<String, dynamic>());

      debugPrint('💳 PAYMENT: Processed result type: ${paymentResult.type}');
      debugPrint(
          '💳 PAYMENT: Processed result message: ${paymentResult.message}');

      // CRITICAL FIX: Always notify server of payment result (similar to PayU)
      try {
        debugPrint('💳 PAYMENT: CRITICAL FIX: Notifying server of payment result...');
        await _notifyServerOfPaymentResult(paymentResult, 'phonepe_transaction_${DateTime.now().millisecondsSinceEpoch}');
        debugPrint('💳 PAYMENT: Server notification completed successfully');
      } catch (e) {
        debugPrint('❌ PAYMENT: Server notification failed: $e');
        // Continue with payment processing even if server notification fails
      }

      if (!_paymentCompleter!.isCompleted) {
        debugPrint('💳 PAYMENT: Completing payment completer with result');
        _paymentCompleter!.complete(paymentResult);
      } else {
        debugPrint('💳 PAYMENT: Payment completer already completed');
      }

      debugPrint('💳 PAYMENT: ========== TRANSACTION END (SUCCESS) ==========');
      return paymentResult;
    } catch (e, stackTrace) {
      debugPrint('❌ PAYMENT: TRANSACTION EXCEPTION');
      debugPrint('❌ PAYMENT: Exception Type: ${e.runtimeType}');
      debugPrint('❌ PAYMENT: Exception Message: $e');
      debugPrint('❌ PAYMENT: Stack Trace: $stackTrace');

      // Cancel timeout timer
      debugPrint('💳 PAYMENT: Cancelling timeout timer due to exception...');
      _paymentTimeoutTimer?.cancel();

      // Determine the type of error
      debugPrint('💳 PAYMENT: Analyzing exception type...');
      PaymentResult errorResult;

      if (e is TimeoutException) {
        debugPrint('💳 PAYMENT: Exception identified as TimeoutException');
        errorResult = PaymentResult.timeout();
      } else if (e.toString().toLowerCase().contains('network') ||
          e.toString().toLowerCase().contains('connection')) {
        debugPrint('💳 PAYMENT: Exception identified as Network Error');
        errorResult = PaymentResult.networkError();
      } else if (e.toString().toLowerCase().contains('cancel')) {
        debugPrint('💳 PAYMENT: Exception identified as Cancellation');
        errorResult = PaymentResult.cancelled();
      } else {
        debugPrint('💳 PAYMENT: Exception identified as Generic Failure');
        debugPrint('💳 PAYMENT: Raw exception: ${e.toString()}');

        // Convert raw exception to user-friendly message
        final userFriendlyMessage =
            ErrorHandler.getUserFriendlyMessage(e.toString());
        debugPrint('💳 PAYMENT: User-friendly message: $userFriendlyMessage');

        errorResult = PaymentResult.failed(
          userFriendlyMessage,
          data: {
            'originalError': e.toString(),
            'stackTrace': stackTrace.toString()
          },
        );
      }

      debugPrint('💳 PAYMENT: Error result type: ${errorResult.type}');
      debugPrint('💳 PAYMENT: Error result message: ${errorResult.message}');

      if (!_paymentCompleter!.isCompleted) {
        debugPrint(
            '💳 PAYMENT: Completing payment completer with error result');
        _paymentCompleter!.complete(errorResult);
      } else {
        debugPrint('💳 PAYMENT: Payment completer already completed');
      }

      debugPrint('💳 PAYMENT: ========== TRANSACTION END (ERROR) ==========');
      return errorResult;
    }
  }

  /// Process the raw payment result from PhonePe SDK
  static PaymentResult _processPaymentResult(Map<String, dynamic>? result) {
    debugPrint('📱 RESPONSE: ========== PROCESSING PAYMENT RESULT ==========');
    debugPrint('📱 RESPONSE: Starting result processing...');

    if (result == null) {
      debugPrint('📱 RESPONSE: Result is NULL');
      debugPrint('📱 RESPONSE: Returning invalidResponse for null result');
      debugPrint('📱 RESPONSE: ========== PROCESSING END (NULL) ==========');
      return PaymentResult.invalidResponse('Null response from PhonePe SDK');
    }

    debugPrint('📱 RESPONSE: Result is not null, analyzing...');
    debugPrint('📱 RESPONSE: Result type: ${result.runtimeType}');
    debugPrint('📱 RESPONSE: Result keys: ${result.keys.toList()}');
    debugPrint('📱 RESPONSE: Full result data: $result');

    try {
      debugPrint('📱 RESPONSE: Extracting status from result...');
      final rawStatus = result['status'];
      debugPrint(
          '📱 RESPONSE: Raw status value: $rawStatus (${rawStatus.runtimeType})');

      final status = rawStatus?.toString().toUpperCase();
      debugPrint('📱 RESPONSE: Processed status: "$status"');

      debugPrint('📱 RESPONSE: Analyzing status value...');
      switch (status) {
        case 'SUCCESS':
        case 'SUCCESSFUL':
          debugPrint('✅ RESPONSE: Status identified as SUCCESS');
          debugPrint('📱 RESPONSE: Creating success PaymentResult');
          debugPrint('📱 RESPONSE: Success data: $result');

          // CRITICAL FIX: Additional validation for success result according to PhonePe docs
          try {
            // Validate success response has required fields
            final transactionId = result['transactionId']?.toString();
            final amount = result['amount']?.toString();

            debugPrint('✅ RESPONSE: Transaction ID: $transactionId');
            debugPrint('✅ RESPONSE: Amount: $amount');

            debugPrint('📱 RESPONSE: ========== PROCESSING END (SUCCESS) ==========');
            return PaymentResult.success(result);
          } catch (e) {
            debugPrint('❌ RESPONSE: Error creating success result: $e');
            debugPrint('📱 RESPONSE: ========== PROCESSING END (SUCCESS ERROR) ==========');
            return PaymentResult.failed('Payment completed but result processing failed');
          }

        case 'FAILURE':
          debugPrint('❌ RESPONSE: Status identified as FAILURE');
          final rawError = result['error']?.toString() ?? 'Payment failed';
          final errorCode = result['code']?.toString();
          debugPrint('📱 RESPONSE: Raw error message: "$rawError"');
          debugPrint('📱 RESPONSE: Error code: "$errorCode"');
          debugPrint('📱 RESPONSE: Failure data: $result');

          // Convert raw error to user-friendly message
          final userFriendlyError =
              ErrorHandler.getUserFriendlyMessage(rawError);
          debugPrint('📱 RESPONSE: User-friendly error: "$userFriendlyError"');

          debugPrint(
              '📱 RESPONSE: ========== PROCESSING END (FAILURE) ==========');
          return PaymentResult.failed(userFriendlyError,
              errorCode: errorCode, data: result);

        case 'CANCELLED':
        case 'CANCELED':
          debugPrint('🚫 RESPONSE: Status identified as CANCELLED/CANCELED');
          debugPrint('📱 RESPONSE: Creating cancelled PaymentResult');
          debugPrint(
              '📱 RESPONSE: ========== PROCESSING END (CANCELLED) ==========');
          return PaymentResult.cancelled();

        case 'PENDING':
          debugPrint('⏳ RESPONSE: Status identified as PENDING');
          debugPrint('📱 RESPONSE: According to PhonePe docs, should retry until SUCCESS or FAILED');
          debugPrint('📱 RESPONSE: Pending data: $result');
          debugPrint('📱 RESPONSE: ========== PROCESSING END (PENDING) ==========');
          // According to PhonePe documentation: "For Pending, you should retry until the status changes to Successful or Failed"
          return PaymentResult.pending(
            'Payment is being processed. Please wait while we verify the transaction status.',
            data: result,
          );

        case 'INTERRUPTED':
          debugPrint('🔄 RESPONSE: Status identified as INTERRUPTED');
          debugPrint('📱 RESPONSE: Payment flow was interrupted');
          debugPrint('📱 RESPONSE: Interrupted data: $result');
          debugPrint('📱 RESPONSE: ========== PROCESSING END (INTERRUPTED) ==========');
          return PaymentResult.interrupted(
            'Payment was interrupted. Please try again.',
            data: result,
          );

        default:
          debugPrint('❓ RESPONSE: Status not recognized: "$status"');
          debugPrint('📱 RESPONSE: Performing pattern matching analysis...');

          // Check for specific error patterns
          final errorMessage = result['error']?.toString() ?? '';
          final resultString = result.toString().toLowerCase();

          debugPrint('📱 RESPONSE: Error message: "$errorMessage"');
          debugPrint(
              '📱 RESPONSE: Result string (lowercase): "${resultString.length > 200 ? "${resultString.substring(0, 200)}..." : resultString}"');

          if (resultString.contains('cancel') ||
              resultString.contains('abort')) {
            debugPrint('🚫 RESPONSE: Pattern match: CANCELLATION detected');
            debugPrint(
                '📱 RESPONSE: ========== PROCESSING END (PATTERN: CANCEL) ==========');
            return PaymentResult.cancelled();
          } else if (resultString.contains('timeout')) {
            debugPrint('⏰ RESPONSE: Pattern match: TIMEOUT detected');
            debugPrint(
                '📱 RESPONSE: ========== PROCESSING END (PATTERN: TIMEOUT) ==========');
            return PaymentResult.timeout();
          } else if (resultString.contains('network') ||
              resultString.contains('connection')) {
            debugPrint('🌐 RESPONSE: Pattern match: NETWORK ERROR detected');
            debugPrint(
                '📱 RESPONSE: ========== PROCESSING END (PATTERN: NETWORK) ==========');
            return PaymentResult.networkError();
          } else if (resultString.contains('crash') ||
              resultString.contains('exception')) {
            debugPrint('💥 RESPONSE: Pattern match: APP CRASH detected');
            debugPrint(
                '📱 RESPONSE: ========== PROCESSING END (PATTERN: CRASH) ==========');
            return PaymentResult.appCrash();
          } else {
            debugPrint('❓ RESPONSE: No pattern match found, returning unknown');
            final unknownMessage =
                'Unknown status: $status, Error: $errorMessage';
            debugPrint('📱 RESPONSE: Unknown message: "$unknownMessage"');
            debugPrint(
                '📱 RESPONSE: ========== PROCESSING END (UNKNOWN) ==========');
            return PaymentResult.unknown(unknownMessage);
          }
      }
    } catch (e, stackTrace) {
      debugPrint('❌ RESPONSE: EXCEPTION during result processing');
      debugPrint('📱 RESPONSE: Exception type: ${e.runtimeType}');
      debugPrint('📱 RESPONSE: Exception message: $e');
      debugPrint('📱 RESPONSE: Stack trace: $stackTrace');

      final errorMessage = 'Error processing result: $e';
      debugPrint('📱 RESPONSE: Returning invalidResponse: "$errorMessage"');
      debugPrint(
          '📱 RESPONSE: ========== PROCESSING END (EXCEPTION) ==========');
      return PaymentResult.invalidResponse(errorMessage);
    }
  }

  /// Cancel any existing payment flow
  static void _cancelExistingPayment() {
    debugPrint('🚫 PHONEPE: ========== CANCELLING EXISTING PAYMENT ==========');
    debugPrint('🚫 PHONEPE: Checking for existing payment flows...');

    if (_paymentTimeoutTimer != null) {
      debugPrint('🚫 PHONEPE: Cancelling existing timeout timer...');
      _paymentTimeoutTimer?.cancel();
      debugPrint('🚫 PHONEPE: Timeout timer cancelled');
    } else {
      debugPrint('🚫 PHONEPE: No timeout timer to cancel');
    }

    if (_paymentCompleter != null) {
      debugPrint(
          '🚫 PHONEPE: Payment completer exists, checking completion status...');
      debugPrint(
          '🚫 PHONEPE: Completer is completed: ${_paymentCompleter!.isCompleted}');

      if (!_paymentCompleter!.isCompleted) {
        debugPrint(
            '🚫 PHONEPE: Completing payment completer with cancelled result');
        _paymentCompleter!.complete(PaymentResult.cancelled());
        debugPrint('🚫 PHONEPE: Payment completer completed with cancellation');
      } else {
        debugPrint(
            '🚫 PHONEPE: Payment completer already completed, no action needed');
      }
    } else {
      debugPrint('🚫 PHONEPE: No payment completer to cancel');
    }

    debugPrint('🚫 PHONEPE: ========== CANCELLATION COMPLETE ==========');
  }

  /// Handle back button press during payment
  static void handleBackButtonPress() {
    debugPrint('🔙 PHONEPE: ========== BACK BUTTON PRESSED ==========');
    debugPrint('🔙 PHONEPE: User pressed back button during payment flow');
    debugPrint('🔙 PHONEPE: Checking payment completer status...');

    if (_paymentCompleter != null) {
      debugPrint('🔙 PHONEPE: Payment completer exists');
      debugPrint(
          '🔙 PHONEPE: Completer is completed: ${_paymentCompleter!.isCompleted}');

      if (!_paymentCompleter!.isCompleted) {
        debugPrint(
            '🔙 PHONEPE: Completing payment with back button pressed result');
        _paymentCompleter!.complete(PaymentResult.backPressed());
        debugPrint('🔙 PHONEPE: Payment completed with back button result');
      } else {
        debugPrint(
            '🔙 PHONEPE: Payment already completed, ignoring back button');
      }
    } else {
      debugPrint('🔙 PHONEPE: No active payment to handle back button for');
    }

    debugPrint(
        '🔙 PHONEPE: ========== BACK BUTTON HANDLING COMPLETE ==========');
  }

  /// Clean up payment resources
  static void cleanup() {
    debugPrint('🧹 PHONEPE: ========== CLEANUP START ==========');
    debugPrint('🧹 PHONEPE: Cleaning up payment resources...');

    if (_paymentTimeoutTimer != null) {
      debugPrint('🧹 PHONEPE: Cancelling timeout timer...');
      _paymentTimeoutTimer?.cancel();
      _paymentTimeoutTimer = null;
      debugPrint('🧹 PHONEPE: Timeout timer cleaned up');
    } else {
      debugPrint('🧹 PHONEPE: No timeout timer to clean up');
    }

    if (_paymentCompleter != null) {
      debugPrint('🧹 PHONEPE: Cleaning up payment completer...');
      debugPrint(
          '🧹 PHONEPE: Completer is completed: ${_paymentCompleter!.isCompleted}');

      if (!_paymentCompleter!.isCompleted) {
        debugPrint(
            '🧹 PHONEPE: Completing payment completer with cancelled result');
        _paymentCompleter!.complete(PaymentResult.cancelled());
      }

      _paymentCompleter = null;
      debugPrint('🧹 PHONEPE: Payment completer cleaned up');
    } else {
      debugPrint('🧹 PHONEPE: No payment completer to clean up');
    }

    // CRITICAL FIX: Clean up server notification callback
    if (_serverNotificationCallback != null) {
      debugPrint('🧹 PHONEPE: Cleaning up server notification callback...');
      _serverNotificationCallback = null;
      debugPrint('🧹 PHONEPE: Server notification callback cleaned up');
    } else {
      debugPrint('🧹 PHONEPE: No server notification callback to clean up');
    }

    debugPrint('🧹 PHONEPE: ========== CLEANUP COMPLETE ==========');
  }

  /// CRITICAL FIX: Register server notification callback (similar to PayU)
  static void registerServerNotificationCallback(Function(PaymentResult, String) callback) {
    _serverNotificationCallback = callback;
    debugPrint('✅ PHONEPE: Server notification callback registered');
  }

  /// CRITICAL FIX: Unregister server notification callback
  static void unregisterServerNotificationCallback() {
    _serverNotificationCallback = null;
    debugPrint('🔄 PHONEPE: Server notification callback unregistered');
  }

  /// CRITICAL FIX: Notify server of payment result
  static Future<void> _notifyServerOfPaymentResult(PaymentResult result, String transactionId) async {
    try {
      debugPrint('🌐 PHONEPE: ========== SERVER NOTIFICATION START ==========');
      debugPrint('🌐 PHONEPE: Notifying server of payment result...');
      debugPrint('🌐 PHONEPE: Result type: ${result.type}');
      debugPrint('🌐 PHONEPE: Transaction ID: $transactionId');

      // ENHANCED: Always call server notification callback with retry
      if (_serverNotificationCallback != null) {
        debugPrint('🌐 PHONEPE: Calling server notification callback...');

        // Retry mechanism for server notification
        int maxRetries = 3;
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
          try {
            debugPrint('🌐 PHONEPE: Server notification attempt $attempt/$maxRetries');
            await _serverNotificationCallback!(result, transactionId);
            debugPrint('✅ PHONEPE: Server notification successful on attempt $attempt');
            break;
          } catch (e) {
            debugPrint('❌ PHONEPE: Server notification failed on attempt $attempt: $e');
            if (attempt == maxRetries) {
              debugPrint('❌ PHONEPE: All server notification attempts failed');
            } else {
              debugPrint('🔄 PHONEPE: Retrying server notification...');
              await Future.delayed(Duration(seconds: attempt * 2));
            }
          }
        }
      } else {
        debugPrint('⚠️ PHONEPE: No server notification callback registered');
      }

      debugPrint('🌐 PHONEPE: ========== SERVER NOTIFICATION END ==========');
    } catch (e) {
      debugPrint('❌ PHONEPE: Critical error in server notification: $e');
    }
  }

  /// Helper methods
  static Future<List<dynamic>?> getInstalledUpiAppsForiOS() =>
      PhonePePaymentSdk.getInstalledUpiAppsForiOS();
  static Future<String?> getUpiAppsForAndroid() =>
      PhonePePaymentSdk.getUpiAppsForAndroid();
}
